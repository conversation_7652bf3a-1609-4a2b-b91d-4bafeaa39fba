# DataKnowledgeTools插件ToolEvent特殊处理实现说明

## 实现概述

已在PluginFilter中为DataKnowledgeTools插件实现了特殊的ToolEvent处理逻辑，确保在调用DataKnowledgeImporter生成的工具时，ToolEvent能够显示正确的格式。

## 主要功能

### 1. 插件识别
- 当插件名为"DataKnowledgeTools"时，自动使用特殊处理逻辑
- 其他插件保持原有的处理方式，确保向后兼容

### 2. 标题格式化
- 从HttpContext的ToolTitleMapping中获取映射的标题
- 将title格式化为"查询场景：{title}"的形式
- 例如：metadata中title为"业绩情况"时，ToolEvent的title为"查询场景：业绩情况"

### 3. 参数显示优化
- **重要改进**：使用参数的描述（Description）而不是参数名（Name）
- 从KernelFunction.Metadata.Parameters中获取参数描述信息
- 格式化为"参数描述（参数值）"的形式
- 例如：`{"comp":"深圳公司"}` 显示为"公司（深圳公司）"而不是"comp（深圳公司）"

### 4. 结果内容提取
- 从返回结果的data字段中提取内容
- 确保ToolEvent显示的是实际的数据内容而不是完整的JSON结构

## 测试用例

### 输入示例

**metadata**:
```json
{
  "id": "3a1b6d31-7d5a-5b04-deb5-46ab3120af9f",
  "name": "scene_3a1b6d31_7d5a_5b04_deb5_46ab3120af9f",
  "title": "业绩情况",
  "description": "了解指定公司的业绩情况明细",
  "input_schema": {
    "type": "object",
    "properties": {
      "comp": {
        "type": "string",
        "description": "公司"
      }
    },
    "required": []
  }
}
```

**调用参数**: `{"comp":"深圳公司"}`

**返回结果**:
```json
{
  "data": "## 表格-1\n\n| 项目\\n/区域 | 认购金额 | 签约金额 | 累计\\n未转认 | 累计\\n未签约 |\n|---|---|---|---|---|\n| 深圳 | 2878084 | 0 | 9003万 | 12157911 |\n| 深圳市龙岗区玉岭路项目 | 2878084 | 0 | 4194万 | 5319204 |\n...",
  "prompt": "基于用户提问，按项目/区域维度进行分组汇总，总结签约、认购情况top3的项目/区域，并",
  "description": "查看公司或区域/项目的当天的业绩情况，包含签约、认购信息",
  "questions": ["今天业绩怎么样","广东区域今天签约情况怎么样"]
}
```

### 期望输出

- **ToolEvent.title**: "查询场景：业绩情况"
- **ToolEvent.arguments**: "公司（深圳公司）"  ← 使用参数描述"公司"而不是参数名"comp"
- **ToolEvent.result**: "## 表格-1\n\n| 项目\\n/区域 | 认购金额 | 签约金额 | 累计\\n未转认 | 累计\\n未签约 |\n|---|---|---|---|---|\n| 深圳 | 2878084 | 0 | 9003万 | 12157911 |\n| 深圳市龙岗区玉岭路项目 | 2878084 | 0 | 4194万 | 5319204 |\n..."

## 代码实现

### 关键修改点

1. **PluginFilter.OnFunctionInvocationAsync**: 添加了对DataKnowledgeTools插件的特殊判断
2. **HandleDataKnowledgeToolsEvent**: 新增方法处理DataKnowledgeTools的特殊逻辑
3. **ExtractDisplayArguments**: 修改为使用参数描述而不是参数名
4. **ExtractDataFromResult**: 新增方法从result.data中提取内容

### 核心逻辑

```csharp
// 针对DataKnowledgeTools插件的特殊处理
if (context.Function.PluginName == "DataKnowledgeTools")
{
    await HandleDataKnowledgeToolsEvent(context, arguments, result);
}
else
{
    // 其他插件的常规处理
    // ...
}
```

### 参数描述映射

```csharp
// 创建参数名到描述的映射
var paramDescriptions = new Dictionary<string, string>();
foreach (var param in function.Metadata.Parameters)
{
    paramDescriptions[param.Name] = param.Description ?? param.Name;
}

// 使用参数描述而不是参数名
string displayName = paramDescriptions.TryGetValue(prop.Name, out var description) 
    ? description 
    : prop.Name;
displayParts.Add($"{displayName}（{value}）");
```

## 特性

- **向后兼容**: 不影响其他插件的正常工作
- **错误处理**: 包含完整的异常处理，确保系统稳定性
- **日志记录**: 详细的日志输出便于调试和监控
- **灵活扩展**: 可以轻松为其他插件添加类似的特殊处理逻辑
- **用户友好**: 显示参数描述而不是技术性的参数名，提升用户体验

## 验证方法

1. 启动应用程序
2. 调用DataKnowledgeTools插件中的任意工具
3. 检查控制台日志确认特殊处理逻辑被触发
4. 验证前端收到的ToolEvent格式是否符合预期
5. 确认arguments显示的是参数描述而不是参数名
